<script>
    import { _shouldInit, _selectedJob, _nextSequenziale, _selectedScanArr, _shouldRecreatePreviews, _isAddingFakeImages, _isEditingScan, _isMultiEditingScan, _mainViewerDidMount, _isShiftPressed, _isCtrlPressed, _user } from '@store';
    import PreviewVisualizerTable from '@components/preview-visualizer/PreviewVisualizerTable.svelte';
    import PreviewVisualizerTableTools from '@components/preview-visualizer/PreviewVisualizerTableTools.svelte';
    import ScanViewers from '@components/open-sea-dragon/ScanViewers.svelte';
    import FakeImagesForm from '@components/preview-visualizer/FakeImagesForm.svelte';
    import EditScanForm from '@components/toolbar/nomenclature/edit-scan/EditScanForm.svelte';
    import MultiEditScanForm from '@components/preview-visualizer/MultiEditScanForm.svelte';
    import NoteScansione from '@components/note/NoteScansione.svelte';
    import CheckPreviewDialog from '@components/preview-visualizer/CheckPreviewDialog.svelte';
    
    import { replace } from 'svelte-spa-router';
    import * as d3 from 'd3-selection';
    import { onMount } from 'svelte';
    
    //smui components
    import Paper, { Title, Content } from '@smui/paper';
    import Button, { Label } from '@smui/button';
    import Tooltip, { Wrapper } from '@smui/tooltip';
    
    let scansioniList = [];
    let ordinatoriList = []
;
    onMount(async () => {
        /* Get scan list */
        await getScansioni();

        /* Get ordinatori list */
        ordinatoriList = await window.electron.database('db-get-ordinatori');

        /* Clear shift and ctrl press */
        _isShiftPressed.set(false);
        _isCtrlPressed.set(false);

        /* Check if any job preview needs to be recreated */
        await checkJobPreviews();
        
        /* Create event listeners */
        createEventListeners();
    })
  
    const getScansioni = async () => {
        scansioniList = await window.electron.database('db-get-scansioni', { idDigit: $_selectedJob?.id_digit }) || [];
        
        /* Set sequenziale to use for the next scan equal to last sequenziale + 1 */
        _nextSequenziale.set(scansioniList.reduce((prev, current) => (prev.sequenziale_scansione > current.sequenziale_scansione) ? prev.sequenziale_scansione + 1  : current.sequenziale_scansione + 1, 1));
    }

    const checkJobPreviews = async () => {
        if(!$_selectedJob) return;
        
        /* get all scans */
        const scanList = await window.electron.database('db-get-scansioni', { idDigit: $_selectedJob.id_digit });

        /* check if preview folder exists */
        const previewsFolderExist = await window.electron.filesystem('fs-path-exists', $_selectedJob.preview_path);

        /* If preview folder doesn't exists, previews should be created for all scans */
        if(!previewsFolderExist) {
            /* Create the preview folder */
		    await window.electron.filesystem("fs-ensure-dir", $_selectedJob.preview_path);
            return scanList;
        } 
        
        let scanWithMissingPreviewsList = [];

        /* Check Tif for which preview should be recreated (modified after preview creation or with no preview at all) */
        const modifiedOrEmptyTifSet = await window.electron.filesystem('fs-check-job-previews', {scan_path: $_selectedJob.scan_path, preview_path: $_selectedJob.preview_path});

        /* add null check */
        if(!modifiedOrEmptyTifSet) return;

        for(const tif of modifiedOrEmptyTifSet) {
            const scanRecord = scanList.find((scan) => scan.id == tif.slice(0, -6));
            if(scanRecord) scanWithMissingPreviewsList.push(scanRecord);
        }

        $_selectedJob.scanWithMissingPreviews = scanWithMissingPreviewsList;
        
        if(scanWithMissingPreviewsList.length > 0)
            _shouldRecreatePreviews.set(true);
    }
    
    const createEventListeners = () => {
        d3.select('#preview-visualizer').on('refresh-scan-table', async (e) => {
            const scanId = e.detail?.scanId;
            const scanIdList = e.detail?.scanIdList;

            await getScansioni();
            
            if(scanIdList)
                selectScanList(scanIdList);
            else if(scanId)
                selectScan(scanId);
            else
                selectFirstScan();
        });

        d3.select('#preview-visualizer').on('order-by-sequenziale', async (e) => {
            scansioniList = scansioniList.sort((a, b) => { return a.sequenziale_scansione - b.sequenziale_scansione });
        });

        d3.select('#preview-visualizer').on('order-by-ordinatore', async (e) => {            
            scansioniList = scansioniList.sort((a, b) => {
                const ordinatoreA = ordinatoriList.find((ordinatore) => ordinatore.metis_code == a.codice_ordinatore_sx);
                const ordinatoreB = ordinatoriList.find((ordinatore) => ordinatore.metis_code == b.codice_ordinatore_sx);
                const visualizationSequenceA = $_selectedJob.is_stampato ? ordinatoreA?.visualization_sequence_stampati || 50 : ordinatoreA?.visualization_sequence || 50;
                const visualizationSequenceB = $_selectedJob.is_stampato ? ordinatoreB?.visualization_sequence_stampati || 50 : ordinatoreB?.visualization_sequence || 50;

                return visualizationSequenceA - visualizationSequenceB;
            });
        });

        d3.select('body').on('keydown', (e) => {
            /* Avoid multiple fires for holding the key */
            if (e.repeat) return;

            if(e.key == 'Shift') _isShiftPressed.set(true);
            if(e.key == 'Control') _isCtrlPressed.set(true);
        });

        d3.select('body').on('keyup', (e) => {
            /* Avoid multiple fires for holding the key */
            if (e.repeat) return;

            if(e.key == 'Shift') _isShiftPressed.set(false);
            if(e.key == 'Control') _isCtrlPressed.set(false);
        });
    }

    const selectFirstScan = () => {
      if(scansioniList.length > 0){
            /* Select first scan automatically */
            $_selectedScanArr.length = 0;
            $_selectedScanArr.push(scansioniList[0]);

            /* Set selected class */
            d3.select('#preview-table').dispatch('set-selected-class-and-scroll-to-element');

            /* Update scan images */
            d3.select('#scan-viewers').dispatch('update-scan-images', {detail: { scan: $_selectedScanArr[0]}});
        }
    }

    const selectScanList = (scanIdList) => {
        /* Select provided scan list */
        if(scansioniList.length > 0){
            $_selectedScanArr.length = 0;

            for (const scanId of scanIdList) {
                const scanToSelect = scansioniList.find((scan) => scan.id == scanId);
                $_selectedScanArr.push(scanToSelect);
            }
        }
    }

    const selectScan = (scanId) => {
        /* Select provided scan */
        if(scansioniList.length > 0){
            $_selectedScanArr.length = 0;
            const scanToSelect = scansioniList.find((scan) => scan.id == scanId);
            $_selectedScanArr.push(scanToSelect);
            
            /* Update scan images */
            d3.select('#scan-viewers').dispatch('update-scan-images', {detail: { scan: $_selectedScanArr[0]}});
        }
    }
    
    const handleBack = async () => {
        if(!$_selectedJob || $_selectedJob.status_digit == 'acquisizione'){
            /* Clear values to trigger initializations */
            _shouldInit.set(false);
            _mainViewerDidMount.set(false);

            /* close scan viewers */
            d3.select('#scan-viewers').dispatch('destroy-viewers');

            /* Go back to acquisition page */
            replace('/acquisizione');
        } else {
            await window.electron.application('restart', {user: $_user, idDigit: $_selectedJob.id_digit});
        }
    }
    </script>

    <CheckPreviewDialog />
    
    <div id='preview-visualizer' class="container" >
        <Paper elevation=5>
            <Wrapper>
                <Title>{$_selectedJob.is_stampato ? $_selectedJob.identifier + ' (' + $_selectedJob.segnatura + ')' : $_selectedJob.identifier}</Title>
                <Tooltip showDelay=0 hideDelay=0>id_digit: {$_selectedJob?.id_digit || ''}</Tooltip>
            </Wrapper>
            <Content>
                <div class="flex-column">
                    <PreviewVisualizerTableTools />
                    <PreviewVisualizerTable options={scansioniList}/>
                    {#if $_selectedScanArr.length > 0}
                        <NoteScansione bind:scanObj={$_selectedScanArr[0]}/>
                    {/if}
                    <Button variant="raised" on:click={handleBack}>
                        <Label>Indietro</Label>
                    </Button>
                </div>
            </Content>
        </Paper>

        {#if $_isAddingFakeImages}
            <FakeImagesForm selectedScan={$_selectedScanArr[0]} {ordinatoriList}/>
        {:else if $_isEditingScan}
            <EditScanForm isFromPreview selectedScan={$_selectedScanArr[0]}/>
        {:else if $_isMultiEditingScan}
            <MultiEditScanForm  selectedScanArr={$_selectedScanArr}/>
        {:else}
            <ScanViewers isFromPreview fullWidth on:ready={$_selectedScanArr.length == 0 ? selectFirstScan : null}/>
        {/if}
    </div>
    
    <style>
        .container {
            display: flex;
            gap: 5px;
            width: 100%;
            height: 100%;
        }
    
        .flex-column {
            display: flex;
            flex-direction: column;
            height: 100%;
            gap: 10px;
        }

        * :global(.smui-paper) {
            height: calc(100% - 48px);
        }
    
        * :global(.smui-paper__content) {
            height: calc(100% - 24px);
        }

        * :global(.smui-paper__title) {
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
        }
    </style>